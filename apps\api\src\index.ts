import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

export interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface ChatRequest {
  messages: ChatMessage[];
  model?: string;
  temperature?: number;
  max_tokens?: number;
}

export interface ChatResponse {
  choices: {
    message: {
      content: string;
    };
  }[];
}

export function buildServer() {
  const app = express();

  // Middleware
  app.use(cors({
    origin: process.env.NODE_ENV === 'production' 
      ? ['https://your-domain.com'] // Replace with your production domain
      : ['http://localhost:5173', 'http://localhost:3000'], // Vite default port
    credentials: true
  }));
  app.use(express.json());

  // Health check endpoint
  app.get('/health', (req, res) => {
    res.json({ status: 'ok', timestamp: new Date().toISOString() });
  });

  // Chat proxy endpoint
  app.post('/api/chat', async (req, res) => {
    try {
      const { messages, model, temperature = 0.7, max_tokens = 150 }: ChatRequest = req.body;

      // Validate request
      if (!messages || !Array.isArray(messages) || messages.length === 0) {
        return res.status(400).json({ 
          error: 'Invalid request: messages array is required and cannot be empty' 
        });
      }

      // Validate API key
      const apiKey = process.env.OPENROUTER_API_KEY;
      if (!apiKey) {
        console.error('OPENROUTER_API_KEY not found in environment variables');
        return res.status(500).json({ 
          error: 'Server configuration error: API key not configured' 
        });
      }

      // Default model if not provided
      const selectedModel = model || 'anthropic/claude-3-haiku';

      // Forward request to OpenRouter
      const openRouterResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'http://localhost:3000', // Replace with your domain
          'X-Title': 'BM Essay Tutor'
        },
        body: JSON.stringify({
          messages,
          model: selectedModel,
          temperature,
          max_tokens
        })
      });

      if (!openRouterResponse.ok) {
        const errorText = await openRouterResponse.text();
        console.error('OpenRouter API error:', openRouterResponse.status, errorText);
        return res.status(openRouterResponse.status).json({ 
          error: 'Failed to get response from AI service',
          details: errorText
        });
      }

      const data: ChatResponse = await openRouterResponse.json();
      
      // Forward the response back to the client
      res.json(data);

    } catch (error) {
      console.error('Error in /api/chat:', error);
      res.status(500).json({ 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  return app;
}

// Start server if this file is run directly
const app = buildServer();
const port = process.env.PORT || 3001;

app.listen(port, () => {
  console.log(`🚀 API server running on http://localhost:${port}`);
  console.log(`📋 Health check: http://localhost:${port}/health`);
  console.log(`💬 Chat endpoint: http://localhost:${port}/api/chat`);
});
