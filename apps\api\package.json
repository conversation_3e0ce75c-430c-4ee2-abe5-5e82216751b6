{"name": "api", "version": "0.0.1", "private": true, "type": "module", "scripts": {"dev": "tsx watch src/index.ts", "test": "vitest run --coverage", "test:watch": "vitest", "build": "tsc", "start": "node dist/index.js", "lint": "eslint . --ext .js,.ts"}, "dependencies": {"vitest": "^1", "@vitest/coverage-c8": "^0.10", "supertest": "^7", "tsx": "^4", "ts-node-dev": "^3", "express": "^4.18.2", "dotenv": "^16.5.0", "cors": "^2.8.5"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/node": "^20.11.18", "@typescript-eslint/eslint-plugin": "^5.54.0", "@typescript-eslint/parser": "^5.54.0", "eslint": "^8.50.0", "tsx": "^4.7.0", "typescript": "^5.5.4"}}